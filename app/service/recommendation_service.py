########################################################
# 推荐问题生成服务 - 基于对话上下文和记忆信息生成相关推荐问题
########################################################

import json
from datetime import datetime
from typing import Any, Dict, List

from configs.lion_config import get_value
from service.ai_client import send_to_ai
from service.enhanced_memory_service import EnhancedMemoryService
from utils.logger import logger


class RecommendationService:
    """推荐问题生成服务"""

    def __init__(self):
        self.memory_service = EnhancedMemoryService()

    def generate_recommended_questions(
        self, user_input: str, ai_response: str, user_id: str, conversation_context: str = "", max_questions: int = 3
    ) -> List[str]:
        """
        基于对话上下文和记忆信息生成推荐问题

        Args:
            user_input: 用户的输入问题
            ai_response: AI的回答内容
            user_id: 用户ID
            conversation_context: 对话上下文（可选）
            max_questions: 最大推荐问题数量

        Returns:
            推荐问题列表
        """
        try:
            # 1. 检索相关记忆信息
            memory_result = self.memory_service.retrieve_memory_for_conversation(user_input, user_id, max_results=5)

            # 2. 构建推荐问题生成的上下文
            context_data = self._build_recommendation_context(
                user_input, ai_response, memory_result, conversation_context
            )

            # 3. 调用AI生成推荐问题
            recommended_questions = self._call_ai_for_recommendations(context_data, max_questions)

            # 4. 过滤和优化推荐问题
            filtered_questions = self._filter_and_optimize_questions(recommended_questions, user_input)

            logger.info(f"为用户 {user_id} 生成了 {len(filtered_questions)} 个推荐问题")
            return filtered_questions

        except Exception as e:
            logger.error(f"生成推荐问题失败: {str(e)}")
            return self._get_fallback_questions(user_input, ai_response)

    def _build_recommendation_context(
        self, user_input: str, ai_response: str, memory_result: Dict[str, Any], conversation_context: str
    ) -> Dict[str, Any]:
        """构建推荐问题生成的上下文数据"""

        # 提取记忆信息
        persons = memory_result.get("persons", [])
        events = memory_result.get("events", [])

        # 构建人物信息摘要
        person_summaries = [
            {
                "name": person.get("canonical_name", ""),
                "profile": person.get("profile_summary", ""),
                "key_attributes": person.get("key_attributes", {}),
            }
            for person in persons[:3]  # 最多使用3个相关人物
        ]

        # 构建事件信息摘要
        event_summaries = []
        for event in events[:3]:  # 最多使用3个相关事件
            summary = {
                "description": event.get("description_text", ""),
                "location": event.get("location", ""),
                "sentiment": event.get("sentiment", ""),
            }
            event_summaries.append(summary)

        context_data = {
            "user_question": user_input,
            "ai_answer": ai_response,
            "conversation_context": conversation_context,
            "related_persons": person_summaries,
            "related_events": event_summaries,
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        return context_data

    def _call_ai_for_recommendations(self, context_data: Dict[str, Any], max_questions: int) -> List[str]:
        """调用AI生成推荐问题"""

        # 获取推荐问题生成的系统提示词
        system_prompt = str(get_value("humanrelation.recommendation_prompt", self._get_default_recommendation_prompt()))

        # 构建请求
        query_input = {
            "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": json.dumps(context_data, ensure_ascii=False)},
            ],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 800,
        }

        response = send_to_ai(query_input)
        response_data = json.loads(response.text)
        ai_content = response_data["choices"][0]["message"]["content"].strip()

        # 解析AI返回的推荐问题
        return self._parse_ai_recommendations(ai_content, max_questions)

    def _parse_ai_recommendations(self, ai_content: str, max_questions: int) -> List[str]:
        """解析AI返回的推荐问题"""
        try:
            # 尝试解析JSON格式
            if ai_content.startswith("```json"):
                ai_content = ai_content[7:-3].strip()
            elif ai_content.startswith("```"):
                ai_content = ai_content[3:-3].strip()

            # 尝试JSON解析
            try:
                parsed = json.loads(ai_content)
                if isinstance(parsed, list):
                    return parsed[:max_questions]
                elif isinstance(parsed, dict) and "questions" in parsed:
                    return parsed["questions"][:max_questions]
            except json.JSONDecodeError:
                pass

            # 如果JSON解析失败，尝试按行分割
            lines = ai_content.strip().split("\n")
            questions = []
            for line in lines:
                line = line.strip()
                # 移除序号和特殊字符
                if line and not line.startswith("#"):
                    # 移除常见的序号格式
                    line = line.lstrip("0123456789.、- ")
                    if line and len(line) > 5:  # 过滤太短的内容
                        questions.append(line)

            return questions[:max_questions]

        except Exception as e:
            logger.error(f"解析AI推荐问题失败: {str(e)}")
            return []

    def _filter_and_optimize_questions(self, questions: List[str], original_question: str) -> List[str]:
        """过滤和优化推荐问题"""
        filtered = []
        original_lower = original_question.lower()

        for question in questions:
            question = question.strip()
            if not question:
                continue

            # 过滤与原问题过于相似的推荐
            if self._is_too_similar(question.lower(), original_lower):
                continue

            # 确保问题以问号结尾
            if not question.endswith("？") and not question.endswith("?"):
                question += "？"

            filtered.append(question)

        return filtered

    def _is_too_similar(self, question1: str, question2: str) -> bool:
        """判断两个问题是否过于相似"""
        # 简单的相似度检查
        if len(question1) < 5 or len(question2) < 5:
            return False

        # 计算重叠字符比例
        set1 = set(question1)
        set2 = set(question2)
        intersection = len(set1 & set2)
        union = len(set1 | set2)

        similarity = intersection / union if union > 0 else 0
        return similarity > 0.7  # 相似度阈值

    def _get_fallback_questions(self, user_input: str, ai_response: str) -> List[str]:
        """获取兜底推荐问题（用户问AI的格式）"""
        fallback_questions = ["还有什么相关信息？", "这个话题有什么细节？", "有什么注意事项？"]

        # 根据用户输入的关键词提供更相关的兜底问题（用户问AI格式）
        if any(keyword in user_input for keyword in ["天气", "气温", "下雨"]):
            return ["明天天气怎么样？", "这周天气如何？", "需要带伞吗？"]
        elif any(keyword in user_input for keyword in ["工作", "项目", "任务"]):
            return ["项目有什么进展？", "还有什么工作安排？", "需要什么资源？"]
        elif any(keyword in user_input for keyword in ["人", "同事", "朋友"]):
            return ["他最近怎么样？", "有什么新消息？", "需要联系他吗？"]
        elif any(keyword in user_input for keyword in ["旅行", "旅游", "去", "玩"]):
            return ["有什么好玩的地方？", "需要准备什么？", "怎么规划行程？"]

        return fallback_questions[:3]

    def _get_default_recommendation_prompt(self) -> str:
        """获取默认的推荐问题生成提示词"""
        return """你是一个智能的问题推荐助手。基于用户的问题、AI的回答以及相关的人物和事件记忆，生成3个用户可能想继续问AI的后续问题。

要求：
1. 推荐的问题应该与当前对话主题相关
2. 问题应该能够深入探讨或扩展当前话题
3. 利用提供的人物档案和事件记忆信息，生成个性化的问题
4. 问题应该自然、友好，符合中文表达习惯
5. 避免重复用户已经问过的问题
6. **重要**：生成的是用户问AI的问题，不是AI问用户的问题

问题格式示例：
- ✅ 正确："日本哪些城市值得去？"（用户问AI）
- ❌ 错误："你计划去日本哪些城市？"（AI问用户）

请以JSON数组格式返回推荐问题，例如：
["问题1？", "问题2？", "问题3？"]

输入数据包含：
- user_question: 用户的原始问题
- ai_answer: AI的回答
- conversation_context: 对话上下文
- related_persons: 相关人物信息
- related_events: 相关事件信息
- current_time: 当前时间"""
