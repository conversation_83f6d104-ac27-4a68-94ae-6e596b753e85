########################################################
# 头像分配服务 - 为新用户随机分配头像
########################################################

import json
import random
from typing import Dict, List, Optional

from configs.lion_config import get_value
from utils.logger import logger


class AvatarService:
    """头像分配服务"""

    def __init__(self):
        # 角色关键词映射 - 按年龄和性别细分
        self.role_keywords = {
            # 家庭角色
            "妈妈": ["妈妈", "母亲", "妈", "老妈", "娘"],
            "爸爸": ["爸爸", "父亲", "爸", "老爸", "爹"],
            "儿子": ["儿子", "儿", "小子"],
            "女儿": ["女儿", "女", "闺女", "丫头"],
            "爷爷": ["爷爷", "爷", "祖父"],
            "奶奶": ["奶奶", "奶", "祖母"],
            # 年龄+性别组合的朋友/同事
            "老年男": ["老爷子", "老大爷", "老先生", "老伯", "老叔"],
            "老年女": ["老太太", "老奶奶", "老阿姨", "老婆婆"],
            "中年男": ["大叔", "叔叔", "老兄", "大哥", "老王", "老李", "老张"],
            "中年女": ["阿姨", "大姐", "老姐", "大嫂"],
            "青年男": ["兄弟", "哥们", "小伙", "小弟", "帅哥", "男生"],
            "青年女": ["闺蜜", "姐妹", "小妹", "美女", "女生", "小姐姐"],
            "小孩": ["孩子", "小朋友", "宝宝", "娃娃", "小家伙"],
            # 通用角色（兜底）
            "朋友": ["朋友", "好友"],
            "同事": ["同事", "同僚", "工友", "同班", "同学"],
        }

    def _load_avatar_pools_from_lion(self) -> Dict[str, List[str]]:
        """从Lion配置中心加载头像资源池"""
        try:
            # 从Lion配置中心获取头像配置
            avatar_config_str = get_value("humanrelation.avatar_pools", "")

            if avatar_config_str:
                avatar_pools = json.loads(avatar_config_str)
                logger.info(f"从Lion配置中心加载头像资源池成功，包含 {len(avatar_pools)} 个角色")
                return avatar_pools
            else:
                logger.warning("Lion配置中心未找到头像配置，使用默认配置")
                return self._get_default_avatar_pools()

        except Exception as e:
            logger.error(f"从Lion配置中心加载头像配置失败: {e}，使用默认配置")
            return self._get_default_avatar_pools()

    def _get_default_avatar_pools(self) -> Dict[str, List[str]]:
        """获取默认头像资源池（当Lion配置中心无配置时的兜底方案）"""
        logger.warning("使用默认头像配置，请在Lion配置中心添加 humanrelation.avatar_pools 配置")
        return {"默认": ["https://via.placeholder.com/150x150/cccccc/666666?text=Avatar"]}

    def detect_role_from_name(self, name: str) -> Optional[str]:
        """从姓名中检测角色"""
        if not name:
            return None

        name = name.strip()

        # 检查是否包含角色关键词
        for role, keywords in self.role_keywords.items():
            for keyword in keywords:
                if keyword in name:
                    logger.info(f"从姓名 '{name}' 中检测到角色: {role}")
                    return role

        return None

    def detect_gender_from_name(self, name: str) -> Optional[str]:
        """使用AI从姓名中检测性别倾向"""
        if not name:
            return None

        name = name.strip()

        try:
            import json

            from service.ai_client import send_to_ai

            prompt = f"""请判断姓名"{name}"的性别倾向。

规则：
1. 只回答"男"或"女"或"未知"
2. 基于中文姓名的常见特征判断
3. 如果无法确定，回答"未知"
4. 不要解释，只回答结果

姓名：{name}
性别："""

            query_input = {
                "model": "gpt-4o-mini",
                "messages": [{"role": "user", "content": prompt}],
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 10,
            }

            response = send_to_ai(query_input, need_logger=False)

            if response and response.status_code == 200:
                response_data = json.loads(response.text)
                result = response_data["choices"][0]["message"]["content"].strip()

                if result in ["男", "女"]:
                    logger.info(f"AI判断姓名 '{name}' 的性别为: {result}")
                    return result
                else:
                    logger.info(f"AI无法确定姓名 '{name}' 的性别，返回: {result}")
                    return None
            else:
                logger.warning(f"AI性别判断失败，姓名: {name}")
                return None

        except Exception as e:
            logger.error(f"AI性别判断出错: {e}，姓名: {name}")
            return None

    def _infer_gender_from_relationships(self, relationships: List[str]) -> Optional[str]:
        """从关系描述中推断性别"""
        if not relationships:
            return None

        # 明确的性别指示词
        male_indicators = [
            "男朋友",
            "老公",
            "丈夫",
            "男同事",
            "男同学",
            "男友",
            "先生",
            "哥哥",
            "弟弟",
            "儿子",
            "爸爸",
            "父亲",
            "爷爷",
            "外公",
        ]
        female_indicators = [
            "女朋友",
            "老婆",
            "妻子",
            "女同事",
            "女同学",
            "女友",
            "女士",
            "姐姐",
            "妹妹",
            "女儿",
            "妈妈",
            "母亲",
            "奶奶",
            "外婆",
        ]

        for relationship in relationships:
            relationship = relationship.strip().lower()

            # 检查男性指示词
            for indicator in male_indicators:
                if indicator in relationship:
                    logger.info(f"从关系 '{relationship}' 中推断出性别: 男")
                    return "男"

            # 检查女性指示词
            for indicator in female_indicators:
                if indicator in relationship:
                    logger.info(f"从关系 '{relationship}' 中推断出性别: 女")
                    return "女"

        return None

    def detect_role_from_relationships(self, relationships: List[str]) -> Optional[str]:
        """从关系列表中检测角色"""
        if not relationships:
            return None

        # 检查关系列表中的角色关键词
        for relationship in relationships:
            if not relationship:
                continue

            for role, keywords in self.role_keywords.items():
                for keyword in keywords:
                    if keyword in relationship:
                        logger.info(f"从关系 '{relationship}' 中检测到角色: {role}")
                        return role

        return None

    def detect_role_from_attributes(self, key_attributes: Dict) -> Optional[str]:
        """从关键属性中检测角色（只检查特定的角色相关字段）"""
        if not key_attributes:
            return None

        # 只检查可能包含角色信息的特定字段
        role_related_keys = [
            "角色",
            "身份",
            "关系",
            "称呼",
            "职位",
            "职业",
            "工作",
            "家庭角色",
            "社会角色",
            "亲属关系",
            "职业身份",
        ]

        for key, value in key_attributes.items():
            # 只检查角色相关的字段
            if not any(role_key in key for role_key in role_related_keys):
                continue

            if not value or not isinstance(value, (str, int, float)):
                continue

            value_str = str(value).strip()
            if not value_str:
                continue

            for role, keywords in self.role_keywords.items():
                for keyword in keywords:
                    if keyword in value_str:
                        logger.info(f"从属性 '{key}: {value}' 中检测到角色: {role}")
                        return role

        return None

    def get_random_avatar_for_role(self, role: str) -> str:
        """为指定角色获取随机头像"""
        # 实时从Lion配置中心获取头像资源池
        avatar_pools = self._load_avatar_pools_from_lion()

        if role in avatar_pools:
            avatar_list = avatar_pools[role]
            selected_avatar = random.choice(avatar_list)
            logger.info(f"为角色 '{role}' 随机选择头像: {selected_avatar}")
            return selected_avatar
        else:
            # 如果角色不存在，使用默认头像
            default_avatar = random.choice(avatar_pools["默认"])
            logger.info(f"角色 '{role}' 不存在，使用默认头像: {default_avatar}")
            return default_avatar

    def assign_avatar_for_person(
        self, canonical_name: str = "", relationships: List[str] = None, key_attributes: Dict = None
    ) -> str:
        """为人员分配头像"""
        relationships = relationships or []
        key_attributes = key_attributes or {}

        logger.info(f"开始为人员分配头像 - 姓名: {canonical_name}, 关系: {relationships}, 属性: {key_attributes}")

        # 按优先级检测角色
        detected_role = None

        # 1. 首先从姓名检测
        detected_role = self.detect_role_from_name(canonical_name)

        # 2. 如果姓名中没有检测到，从关系中检测
        if not detected_role:
            detected_role = self.detect_role_from_relationships(relationships)

        # 3. 如果关系中没有检测到，从属性中检测
        if not detected_role:
            detected_role = self.detect_role_from_attributes(key_attributes)

        # 4. 如果检测到通用"朋友"或"同事"角色，尝试结合性别细化
        if detected_role in ["朋友", "同事"]:
            # 优先从key_attributes中获取性别信息
            gender = None
            if key_attributes and "基本信息" in key_attributes:
                basic_info = key_attributes["基本信息"]
                if isinstance(basic_info, dict) and "性别" in basic_info:
                    gender = basic_info["性别"]
                    logger.info(f"从key_attributes中获取到性别: {gender}")

            # 如果key_attributes中没有性别信息，尝试从关系中推断
            if not gender:
                gender = self._infer_gender_from_relationships(relationships)

            # 如果关系也无法推断性别，最后使用AI判断姓名
            if not gender:
                gender = self.detect_gender_from_name(canonical_name)

            if gender == "男":
                detected_role = "青年男"
                logger.info(f"检测到男性{detected_role} '{canonical_name}'，使用青年男角色")
            elif gender == "女":
                detected_role = "青年女"
                logger.info(f"检测到女性{detected_role} '{canonical_name}'，使用青年女角色")
            else:
                logger.info(f"无法确定{detected_role} '{canonical_name}' 的性别，保持原角色")

        # 5. 如果都没有检测到，使用默认角色，但尝试根据性别细化
        if not detected_role:
            # 优先从key_attributes中获取性别信息
            gender = None
            if key_attributes and "基本信息" in key_attributes:
                basic_info = key_attributes["基本信息"]
                if isinstance(basic_info, dict) and "性别" in basic_info:
                    gender = basic_info["性别"]
                    logger.info(f"从key_attributes中获取到性别: {gender}")

            # 如果key_attributes中没有性别信息，尝试从关系中推断
            if not gender:
                gender = self._infer_gender_from_relationships(relationships)

            # 如果关系也无法推断性别，最后使用AI判断姓名
            if not gender:
                gender = self.detect_gender_from_name(canonical_name)

            if gender == "男":
                detected_role = "青年男"
                logger.info(f"未检测到特定角色，但检测到男性，使用青年男角色")
            elif gender == "女":
                detected_role = "青年女"
                logger.info(f"未检测到特定角色，但检测到女性，使用青年女角色")
            else:
                detected_role = "默认"
                logger.info(f"未检测到特定角色和性别，使用默认角色")

        # 获取对应角色的随机头像
        avatar_url = self.get_random_avatar_for_role(detected_role)

        logger.info(f"为人员 '{canonical_name}' 分配头像完成 - 角色: {detected_role}, 头像: {avatar_url}")
        return avatar_url


# 创建全局实例
avatar_service = AvatarService()


def assign_avatar_for_new_person(
    canonical_name: str = "", relationships: List[str] = None, key_attributes: Dict = None
) -> str:
    """为新人员分配头像的便捷函数"""
    return avatar_service.assign_avatar_for_person(
        canonical_name=canonical_name, relationships=relationships, key_attributes=key_attributes
    )
