# 话题推荐功能 Lion 配置项

## 概述

话题推荐功能需要在 Lion 配置中心添加以下配置项，用于控制话题推荐的生成策略和行为。

## 必需配置项

### 1. 话题推荐 AI 提示词

**配置键**: `humanrelation.topic_recommendation_prompt`
**说明**: 用于生成话题推荐的 AI 提示词
**默认值**: 见服务代码中的默认提示词

**当前默认提示词内容**:

```
你是一个智能生活助手，为用户推荐高度个性化的聊天话题。请深度分析用户档案信息，结合其背景、兴趣、经历，生成贴合其个人特色的自然话题。

话题要求：
1. **高度个性化**：必须基于用户的具体信息（地域背景、职业、爱好、经历等）
2. **口语化自然**：像朋友间随意聊天，简短直接（5-10个字）
3. **细节化关联**：体现对用户具体喜好、经历的了解和关心
4. **情感共鸣**：能引起用户"你真了解我"的感觉
5. **时效相关**：结合当前时间、季节、用户近期活动
6. **避免泛泛而谈**：拒绝"最近在做什么"这种通用问题
7. **体现关注度**：让用户感受到被用心了解和关怀

个性化策略：
- 地域背景：如"上海→北京"可问"北京有哪些地方让你想起上海？"
- 具体喜好：如"喜欢汉堡"可问"最近吃到什么好吃的汉堡？"
- 生活状态：如"已婚+旅行"可问"和老公还有什么旅行计划？"
- 个人特色：如"喜欢向日葵"可问"向日葵季节有去看吗？"
- 职业/兴趣：结合具体工作或爱好细节
- 近期事件：基于最近参与的活动或经历

话题分类优先级：
- food（美食）：结合具体饮食喜好 - 优先级4-5
- hobby（兴趣）：基于具体兴趣爱好 - 优先级4-5
- life（生活）：联系具体生活背景和状态 - 优先级4-5
- entertainment（娱乐）：考虑个人娱乐偏好 - 优先级3-4
- travel（旅行）：结合去过的地方和旅行习惯 - 优先级3-4
- work（工作）：基于具体职业和工作状态 - 优先级3

请返回JSON数组格式，每个话题包含：
- topic: 个性化话题内容（口语化问句，5-10个字，体现对用户的了解）
- category: 分类标签
- description: 个性化依据说明
- priority: 优先级（1-5）

示例（基于用户档案的个性化话题）：
[
  {
    "topic": "最近吃到什么好汉堡？",
    "category": "food",
    "description": "基于用户对加州汉堡的喜好",
    "priority": 5
  },
  {
    "topic": "北京哪里像上海？",
    "category": "life",
    "description": "结合用户上海→北京的地域背景",
    "priority": 4
  },
  {
    "topic": "和老公计划去哪玩？",
    "category": "travel",
    "description": "基于用户已婚且热爱旅行的状态",
    "priority": 4
  },
  {
    "topic": "向日葵开了去看了吗？",
    "category": "hobby",
    "description": "基于用户喜欢向日葵的个人偏好",
    "priority": 4
  },
  {
    "topic": "最近工作忙不忙？",
    "category": "work",
    "description": "关心都市职场女性的工作状态",
    "priority": 3
  }
]

重要提醒：
- 仔细阅读user_profile中的详细信息
- 结合recent_events中的具体活动
- 避免生成"最近在做什么"、"有什么计划"这种通用话题
- 每个话题都应体现对用户的深度了解

用户信息：
- user_profile: 用户档案（包含详细的个人背景、喜好、经历）
- recent_events: 近期事件（用户最近的活动和经历）
- reminders: 提醒事项（用户关注的重要事情）
- current_time: 当前时间（季节、时段信息）
```

### 2. 话题推荐使用的 AI 模型

**配置键**: `humanrelation.memory_extraction_model`
**默认值**: `gpt-4o-mini`
**说明**: 话题推荐功能复用记忆提取的 AI 模型配置

## 可选配置项

### 3. 事件索引名称

**配置键**: `humanrelation.event_index_name`
**默认值**: `memory_event_store`
**说明**: 用于获取用户事件信息的 ElasticSearch 索引名称

## API 接口说明

### 话题推荐接口

**路径**: `POST /humanrelation/recommend_topics`

**请求参数**:

```json
{
  "user_id": "请求用户ID（必需，用于权限验证）",
  "person_id": "目标人员ID（必需，为谁生成话题推荐）",
  "context": "额外上下文信息（可选，默认空字符串）",
  "max_topics": 5,
  "fast_mode": false
}
```

**参数说明**:

- `user_id`: 必填，请求用户 ID 字符串，用于权限验证
- `person_id`: 必填，目标人员 ID 字符串，为谁生成话题推荐
- `context`: 可选，额外的上下文信息，如"准备和朋友聚餐"
- `max_topics`: 可选，最大推荐话题数量，默认 5 个，限制 1-5 个
- `fast_mode`: 可选，快速模式开关，默认 false
  - `true`: 跳过 AI 调用，直接返回预设话题（响应极快，< 0.5 秒）
  - `false`: 使用 AI 生成个性化话题（响应较慢，1-3 秒）

**响应格式**:

```json
{
  "result": "success",
  "user_id": "test_user",
  "person_id": "person_123",
  "recommended_topics": [
    {
      "topic": "你平时都喜欢吃什么？",
      "category": "food",
      "description": "美食话题，人人都有话说",
      "priority": 4
    }
  ],
  "context_summary": {
    "cached": false,
    "fast_mode": false,
    "user_profile_available": true,
    "events_count": 5,
    "has_reminders": true
  },
  "generated_at": "2024-01-01 12:00:00"
}
```

**context_summary 字段说明**:

- `cached`: 是否使用了缓存结果
- `fast_mode`: 是否使用了快速模式
- `user_profile_available`: 用户档案是否可用
- `events_count`: 获取到的事件数量
- `has_reminders`: 是否有提醒事项

## 使用场景

### 1. 基础话题推荐

```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123"}'
```

### 2. 限制话题数量

```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "max_topics": 3}'
```

### 3. 带上下文的话题推荐

```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "context": "准备参加公司聚会",
    "max_topics": 3
  }'
```

### 4. 快速模式（极速响应）

```bash
curl -X POST /humanrelation/recommend_topics \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "fast_mode": true,
    "max_topics": 3
  }'
```

## 功能特性

1. **智能个性化**: 基于用户档案和近期事件生成个性化话题
2. **生活化有趣**: 生成贴近生活、有趣味性的话题
3. **优先级排序**: AI 会为话题分配优先级，重点推荐美食、兴趣、生活话题（4-5 分）
4. **分类标签**: 每个话题都有分类标签，便于理解和筛选
5. **上下文感知**: 考虑时间、季节和用户背景等上下文因素
6. **备用机制**: 当 AI 生成失败时，提供备用话题保证功能可用
7. **🆕 缓存机制**: 相同请求 5 分钟内使用缓存，提升响应速度
8. **🆕 快速模式**: 支持极速响应，跳过 AI 调用直接返回预设话题
9. **🆕 并行查询**: 用户档案和事件信息并行获取，减少响应时间
10. **🆕 错误恢复**: 完善的错误处理和降级机制

## 性能特性

| 模式     | 响应时间 | 个性化程度 | 适用场景     |
| -------- | -------- | ---------- | ------------ |
| 快速模式 | < 0.5 秒 | 低         | 极速响应需求 |
| 缓存命中 | < 0.5 秒 | 高         | 重复请求     |
| AI 生成  | 1-3 秒   | 高         | 个性化推荐   |

## 注意事项

1. 话题生成需要消耗 AI 模型 token，建议合理设置`max_topics`参数
2. 功能依赖用户档案和事件数据，数据越完整推荐越准确
3. 建议在配置中心设置合适的 AI 提示词以符合业务需求
4. 接口支持并发调用，但需要注意 AI 服务的 QPS 限制
5. 🆕 缓存有效期为 5 分钟，频繁相同请求会自动使用缓存
6. 🆕 快速模式适合对响应时间要求极高的场景
7. 🆕 系统具备完善的降级机制，AI 服务异常时自动返回备用话题

## 故障排查

### 接口响应慢？

1. 尝试使用快速模式：`"fast_mode": true`
2. 检查 AI 服务状态和网络连接
3. 查看日志中的"AI 请求完成，耗时"信息

### AI 生成失败？

1. 检查 Lion 配置 `humanrelation.memory_extraction_model`
2. 确认 AI 服务 token 有效性
3. 查看是否自动降级到备用话题

### 话题质量不佳？

1. 优化 Lion 配置 `humanrelation.topic_recommendation_prompt`
2. 确保用户档案数据完整
3. 提供更具体的 context 参数
