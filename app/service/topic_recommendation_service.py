########################################################
# 话题推荐服务 - 基于用户档案和事件信息生成推荐话题
########################################################

import hashlib
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from configs.lion_config import get_value
from service.ai_client import send_to_ai
from service.enhanced_memory_service import EnhancedMemoryService
from service.ESmemory.es_event_service import get_recent_events, get_user_recent_events_and_reminders
from utils.logger import logger


class TopicRecommendationService:
    """话题推荐服务"""

    def __init__(self):
        self.memory_service = EnhancedMemoryService()
        # 简单的内存缓存 - 缓存用户的话题推荐结果
        self._cache = {}
        self._cache_ttl = 300  # 缓存5分钟

    def _get_cache_key(self, user_id: str, person_id: str, context: str) -> str:
        """生成缓存键 - 确保person隔离"""
        # 基于用户ID、人员ID和上下文生成缓存键，确保不同person的缓存隔离
        content = f"{user_id}:{person_id}:{context}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """从缓存获取结果"""
        if cache_key in self._cache:
            cached_data, timestamp = self._cache[cache_key]
            if datetime.now().timestamp() - timestamp < self._cache_ttl:
                logger.info(f"使用缓存的话题推荐结果: {cache_key}")
                return cached_data
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
        return None

    def _set_cache(self, cache_key: str, data: List[Dict[str, Any]]):
        """设置缓存"""
        self._cache[cache_key] = (data, datetime.now().timestamp())
        # 简单的缓存清理：如果缓存太多就清理一些
        if len(self._cache) > 100:
            # 删除最旧的20个缓存项
            sorted_items = sorted(self._cache.items(), key=lambda x: x[1][1])
            for key, _ in sorted_items[:20]:
                del self._cache[key]

    def generate_recommended_topics(
        self,
        user_id: str,
        person_id: str,
        context: str = "",
        max_topics: int = 5,
        fast_mode: bool = False,  # 新增快速模式
    ) -> Dict[str, Any]:
        """
        基于指定人员档案和事件信息生成推荐话题

        Args:
            user_id: 请求用户ID（用于权限验证）
            person_id: 目标人员ID（为谁生成话题推荐）
            context: 额外的上下文信息
            max_topics: 最大推荐话题数量
            fast_mode: 快速模式，直接返回备用话题

        Returns:
            包含推荐话题的字典
        """
        try:
            logger.info(
                f"开始为person {person_id} 生成话题推荐（请求用户: {user_id}）{'(快速模式)' if fast_mode else ''}"
            )

            # 快速模式：直接返回备用话题
            if fast_mode:
                logger.info("使用快速模式，返回预设话题")
                return {
                    "result": "success",
                    "user_id": user_id,
                    "person_id": person_id,
                    "recommended_topics": self._get_fallback_topics()[:max_topics],
                    "context_summary": {
                        "fast_mode": True,
                        "user_profile_available": False,
                        "events_count": 0,
                        "has_reminders": False,
                    },
                    "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }

            # 优化：检查缓存（使用user_id和person_id确保缓存隔离）
            cache_key = self._get_cache_key(user_id, person_id, context)
            cached_topics = self._get_from_cache(cache_key)
            if cached_topics:
                return {
                    "result": "success",
                    "user_id": user_id,
                    "person_id": person_id,
                    "recommended_topics": cached_topics[:max_topics],
                    "context_summary": {
                        "cached": True,
                        "user_profile_available": True,
                        "events_count": 0,
                        "has_reminders": False,
                    },
                    "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }

            # 优化：并行获取数据（使用person_id获取档案信息）
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                # 并行执行数据获取任务，使用person_id获取指定person的信息
                profile_future = executor.submit(self._get_person_profile_info, user_id, person_id)
                events_future = executor.submit(self._get_events_info, user_id, person_id)

                # 获取结果
                person_profile = profile_future.result()
                events_info = events_future.result()

            # 3. 构建话题推荐的上下文
            context_data = self._build_topic_context(person_profile, events_info, context)

            # 4. 调用AI生成推荐话题
            recommended_topics = self._call_ai_for_topic_recommendations(context_data, max_topics)

            # 优化：缓存结果
            self._set_cache(cache_key, recommended_topics)

            # 5. 格式化返回结果
            result = {
                "result": "success",
                "user_id": user_id,
                "person_id": person_id,
                "recommended_topics": recommended_topics,
                "context_summary": {
                    "user_profile_available": bool(person_profile),
                    "events_count": len(events_info.get("events", [])),
                    "has_reminders": events_info.get("has_reminders", False),
                },
                "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }

            logger.info(f"成功为person {person_id} 生成话题推荐，数量: {len(recommended_topics)}")
            return result

        except Exception as e:
            logger.error(f"为person {person_id} 生成话题推荐失败: {str(e)}")
            import traceback

            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "result": "error",
                "reason": f"生成话题推荐失败: {str(e)}",
                "user_id": user_id,
                "person_id": person_id,
                "recommended_topics": [],
            }

    def _get_user_profile_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户档案信息"""
        try:
            from service.mysql_person_service import get_user_person

            user_person = get_user_person(user_id)
            if user_person:
                return {
                    "canonical_name": user_person.get("canonical_name", ""),
                    "profile_summary": user_person.get("profile_summary", ""),
                    "key_attributes": user_person.get("key_attributes", {}),
                    "relationships": user_person.get("relationships", []),
                }
            return {}
        except Exception as e:
            logger.error(f"获取用户档案失败: {str(e)}")
            return {}

    def _get_person_profile_info(self, user_id: str, person_id: str) -> Dict[str, Any]:
        """获取指定人员的档案信息"""
        try:
            from service.mysql_person_service import get_person_by_id_mysql

            person_result = get_person_by_id_mysql(user_id, person_id)
            if person_result and person_result.get("result") == "success":
                person_data = person_result.get("person", {})
                return {
                    "canonical_name": person_data.get("canonical_name", ""),
                    "profile_summary": person_data.get("profile_summary", ""),
                    "key_attributes": person_data.get("key_attributes", {}),
                    "relationships": person_data.get("relationships", []),
                }
            return {}
        except Exception as e:
            logger.error(f"获取人员档案失败: {str(e)}")
            return {}

    def _get_events_info(self, user_id: str, person_id: str) -> Dict[str, Any]:
        """获取指定人员的事件信息 - 确保person信息隔离"""
        events_info = {"events": [], "reminders": []}

        try:
            # 并行获取事件和提醒信息，确保person数据隔离
            import concurrent.futures

            def get_events():
                try:
                    # 严格按person_id查询该person参与的事件，避免数据混淆
                    from service.ESmemory.es_event_service import search_events_by_participant

                    event_index = get_value("humanrelation.event_index_name", "memory_event_store")

                    logger.info(f"🔍 查询person {person_id} 参与的事件（用户: {user_id}）")

                    # 使用正确的user_id和person_id进行查询，确保数据隔离
                    events_result = search_events_by_participant(event_index, user_id, person_id, 5)
                    if events_result.get("result") == "success":
                        events = events_result.get("events", [])
                        logger.info(f"📋 找到person {person_id} 的事件数量: {len(events)}")
                        return events

                    logger.info(f"⚠️ 未找到person {person_id} 参与的事件")
                    return []
                except Exception as e:
                    logger.error(f"❌ 获取person {person_id} 的事件失败: {str(e)}")
                    return []

            def check_reminders():
                try:
                    # 查询与该person相关的提醒（作为subject_person_id）
                    from my_mysql.sql_client import CLIENT
                    from sqlalchemy import text

                    logger.info(f"🔔 查询person {person_id} 的相关提醒")

                    # 查询subject_person_id为该person的提醒
                    query = text(
                        """
                        SELECT COUNT(*) as count
                        FROM reminders
                        WHERE user_id = :user_id
                        AND subject_person_id = :person_id
                        AND status = 'active'
                    """
                    )

                    result = CLIENT.execute(query, {"user_id": user_id, "person_id": person_id})
                    count = result.fetchone()[0] if result else 0

                    logger.info(f"📌 找到person {person_id} 的提醒数量: {count}")
                    return count > 0

                except Exception as e:
                    logger.error(f"❌ 检查person {person_id} 的提醒失败: {str(e)}")
                    return False

            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                events_future = executor.submit(get_events)
                reminders_future = executor.submit(check_reminders)

                # 获取该person专属的事件信息
                events_list = events_future.result()
                events_info["events"] = events_list

                # 获取该person专属的提醒信息
                has_reminders = reminders_future.result()
                events_info["has_reminders"] = has_reminders

            logger.info(
                f"✅ 成功获取person {person_id} 的专属信息 - 事件:{len(events_info['events'])}个, 提醒:{events_info['has_reminders']}"
            )

        except Exception as e:
            logger.error(f"❌ 获取person {person_id} 的事件信息失败: {str(e)}")

        return events_info

    def _build_topic_context(
        self, user_profile: Dict[str, Any], events_info: Dict[str, Any], additional_context: str
    ) -> Dict[str, Any]:
        """构建话题推荐的上下文数据"""

        # 提取关键事件信息
        recent_events = []
        for event in events_info.get("events", [])[:5]:  # 最多使用5个最近事件
            event_summary = {
                "description": event.get("description_text", ""),
                "location": event.get("location", ""),
                "participants": event.get("participants", []),
                "sentiment": event.get("sentiment", ""),
                "timestamp": event.get("timestamp", ""),
            }
            recent_events.append(event_summary)

        # 获取当前时间信息
        now = datetime.now()
        current_hour = now.hour
        current_weekday = now.weekday()  # 0=周一, 6=周日
        current_season = self._get_current_season(now.month)
        time_period = self._get_time_period(current_hour)

        # 构建上下文数据
        context_data = {
            "user_profile": user_profile,
            "recent_events": recent_events,
            "reminders": events_info.get("reminders", []),
            "additional_context": additional_context,
            "current_time": now.strftime("%Y-%m-%d %H:%M:%S"),
            "time_context": {
                "hour": current_hour,
                "period": time_period,  # morning, afternoon, evening, night
                "weekday": current_weekday,
                "is_weekend": current_weekday >= 5,
                "season": current_season,
            },
            "request_type": "topic_recommendation",
        }

        return context_data

    def _get_current_season(self, month: int) -> str:
        """根据月份获取当前季节"""
        if month in [3, 4, 5]:
            return "spring"
        elif month in [6, 7, 8]:
            return "summer"
        elif month in [9, 10, 11]:
            return "autumn"
        else:
            return "winter"

    def _get_time_period(self, hour: int) -> str:
        """根据小时获取时间段"""
        if 6 <= hour < 12:
            return "morning"
        elif 12 <= hour < 18:
            return "afternoon"
        elif 18 <= hour < 22:
            return "evening"
        else:
            return "night"

    def _call_ai_for_topic_recommendations(self, context_data: Dict[str, Any], max_topics: int) -> List[Dict[str, Any]]:
        """调用AI生成推荐话题"""

        # 获取话题推荐的系统提示词
        system_prompt = str(
            get_value("humanrelation.topic_recommendation_prompt", self._get_default_topic_recommendation_prompt())
        )

        # 恢复传递完整数据，让AI能根据用户档案和事件生成个性化话题
        # simplified_context = self._simplify_context_for_ai(context_data)

        # 记录数据大小
        data_size = len(json.dumps(context_data, ensure_ascii=False))
        logger.info(f"📊 传递完整用户数据: {data_size} 字符")

        # 构建请求 - 传递完整用户信息
        query_input = {
            "model": get_value("humanrelation.memory_extraction_model", "gpt-4o-mini"),
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": json.dumps(context_data, ensure_ascii=False)},
            ],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 1000,  # 恢复到足够的token数量
        }

        try:
            # 记录AI调用开始时间
            import time

            start_time = time.time()

            # 暂时移除超时限制，观察AI真实响应时间
            # response = send_to_ai(query_input, timeout=4)
            response = send_to_ai(query_input)

            # 计算AI调用耗时
            ai_time = time.time() - start_time
            logger.info(f"🤖 AI调用真实耗时: {ai_time:.2f}秒")

            # 检查响应是否有效
            if not response or response.status_code != 200:
                logger.warning(f"AI调用失败，状态码: {response.status_code if response else 'None'}，返回备用话题")
                return self._get_fallback_topics()[:max_topics]

            response_data = json.loads(response.text)
            ai_content = response_data["choices"][0]["message"]["content"].strip()

            logger.info(f"🎯 AI返回内容长度: {len(ai_content)}字符")
            logger.info(f"📝 AI返回原始内容: {ai_content}")

            # 解析AI返回的推荐话题
            return self._parse_ai_topic_recommendations(ai_content, max_topics)

        except Exception as e:
            # 暂时注释掉超时降级，让异常正常抛出以便调试
            logger.error(f"AI调用异常: {str(e)}")
            # return self._get_fallback_topics()[:max_topics]
            raise  # 抛出异常，不使用备用话题

    def _simplify_context_for_ai(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        精简AI输入数据，大幅减少token消耗，提升处理速度
        """
        user_profile = context_data.get("user_profile", {})
        recent_events = context_data.get("recent_events", [])
        time_context = context_data.get("time_context", {})

        simplified = {
            "context": context_data.get("additional_context", "")[:30],  # 限制30字符
            "time": time_context.get("period", ""),  # morning/afternoon/evening/night
            "weekend": time_context.get("is_weekend", False),
        }

        # 只保留用户名（如果有）
        name = user_profile.get("canonical_name", "")
        if name:
            simplified["name"] = name[:10]  # 限制10字符

        # 只保留最新1个事件的简要描述
        if recent_events:
            event_desc = recent_events[0].get("description", "")
            if event_desc:
                simplified["recent"] = event_desc[:20]  # 限制20字符

        return simplified

    def _parse_ai_topic_recommendations(self, ai_content: str, max_topics: int) -> List[Dict[str, Any]]:
        """解析AI返回的话题推荐 - 增强容错版本"""
        try:
            # 清理AI返回的内容
            ai_content = ai_content.strip()

            # 尝试从文本中提取JSON部分
            if "[" in ai_content and "]" in ai_content:
                start = ai_content.find("[")
                end = ai_content.rfind("]") + 1
                json_str = ai_content[start:end]

                try:
                    topics_data = json.loads(json_str)

                    # 确保是列表格式
                    if isinstance(topics_data, list):
                        # 补充缺失的字段
                        processed_topics = []
                        for topic in topics_data[:max_topics]:
                            if isinstance(topic, dict) and "topic" in topic:
                                processed_topic = {
                                    "topic": topic.get("topic", "聊聊最近的生活吧"),
                                    "category": topic.get("category", "life"),
                                    "description": topic.get("description", "日常话题"),
                                    "priority": topic.get("priority", 4),
                                }
                                processed_topics.append(processed_topic)

                        if processed_topics:
                            logger.info(f"成功解析AI话题推荐，数量: {len(processed_topics)}")
                            return processed_topics
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败: {e}")

            # 如果JSON解析失败，尝试按行解析
            logger.info("尝试按行解析AI返回内容")
            lines = ai_content.strip().split("\n")
            topics = []

            for line in lines:
                line = line.strip()
                if line and len(line) > 3:
                    # 移除序号、特殊字符等
                    clean_line = line.lstrip("0123456789.- ").strip('[](){}"')
                    if clean_line and len(clean_line) > 2:
                        topics.append(
                            {"topic": clean_line, "category": "life", "description": "AI生成话题", "priority": 4}
                        )
                        if len(topics) >= max_topics:
                            break

            if topics:
                logger.info(f"按行解析成功，话题数量: {len(topics)}")
                return topics

        except Exception as e:
            logger.warning(f"解析AI话题推荐失败: {str(e)}")

        # 最终降级
        logger.info("AI解析完全失败，使用备用话题")
        return self._get_fallback_topics()[:max_topics]

    def _get_default_topic_recommendation_prompt(self) -> str:
        """获取默认的话题推荐提示词"""
        return """你是一个智能生活助手，为用户推荐高度个性化的聊天话题。请深度分析用户档案信息，结合其背景、兴趣、经历，站在用户的角度，猜测用户可能想问什么问题。

话题要求：
1. **高度个性化**：必须基于用户的具体信息（地域背景、职业、爱好、经历等）
2. **口语化自然**：像用户自己会问的问题，简短直接（5-10个字）
3. **细节化关联**：体现用户对具体事物的关心和兴趣
4. **情感共鸣**：符合用户的思维方式和关注点
5. **时效相关**：结合当前时间、季节、用户近期活动
6. **避免泛泛而谈**：拒绝"今天怎么样"这种通用问题
7. **用户视角**：站在用户角度思考，用户会想了解什么

个性化策略（用户视角）：
- 地域背景：如"上海→北京"用户可能问"北京哪里有好吃的上海菜？"
- 具体喜好：如"喜欢汉堡"用户可能问"附近哪家汉堡最好吃？"
- 生活状态：如"已婚+旅行"用户可能问"适合夫妻的旅行地推荐？"
- 个人特色：如"喜欢向日葵"用户可能问"哪里可以看到向日葵花海？"
- 职业/兴趣：结合具体工作或爱好，用户会关心什么
- 近期事件：基于最近参与的活动，用户可能有什么疑问

话题分类优先级：
- food（美食）：结合具体饮食喜好 - 优先级4-5
- hobby（兴趣）：基于具体兴趣爱好 - 优先级4-5
- life（生活）：联系具体生活背景和状态 - 优先级4-5
- entertainment（娱乐）：考虑个人娱乐偏好 - 优先级3-4
- travel（旅行）：结合去过的地方和旅行习惯 - 优先级3-4
- work（工作）：基于具体职业和工作状态 - 优先级3

请返回JSON数组格式，每个话题包含：
- topic: 个性化话题内容（用户可能会问的问题，5-10个字，口语化）
- category: 分类标签
- description: 个性化依据说明
- priority: 优先级（1-5）

示例（站在用户角度，用户可能会问的问题）：
[
  {
    "topic": "附近哪家汉堡最好吃？",
    "category": "food",
    "description": "基于用户对汉堡的喜好，用户想找好吃的汉堡店",
    "priority": 5
  },
  {
    "topic": "北京哪里有上海菜？",
    "category": "food",
    "description": "用户从上海到北京，可能想念家乡菜",
    "priority": 4
  },
  {
    "topic": "适合夫妻的旅行地推荐？",
    "category": "travel",
    "description": "基于用户已婚状态，想找适合夫妻的旅行目的地",
    "priority": 4
  },
  {
    "topic": "哪里可以看向日葵花海？",
    "category": "hobby",
    "description": "基于用户喜欢向日葵，想知道观赏地点",
    "priority": 4
  },
  {
    "topic": "如何平衡工作和生活？",
    "category": "work",
    "description": "职场人士常见的困惑和关注点",
    "priority": 3
  }
]

重要提醒：
- 仔细阅读user_profile中的详细信息
- 结合recent_events中的具体活动
- 避免生成"今天怎么样"、"有什么建议"这种过于通用的话题
- 每个话题都应该是用户可能会主动问的问题
- 站在用户角度思考：用户会想了解什么、询问什么

用户信息：
- user_profile: 用户档案（包含详细的个人背景、喜好、经历）
- recent_events: 近期事件（用户最近的活动和经历）
- reminders: 提醒事项（用户关注的重要事情）
- current_time: 当前时间（季节、时段信息）
"""

    def _get_fallback_topics(self) -> List[Dict[str, Any]]:
        """获取备用话题（当AI生成失败时使用）- 优化版本"""
        import random

        # 扩展备用话题库 - 用户问AI的问题
        topic_pool = [
            # 美食类 (4-5分) - 用户想了解美食信息
            {"topic": "附近有什么好吃的？", "category": "food", "description": "用户想找附近美食", "priority": 4},
            {"topic": "这道菜怎么做？", "category": "food", "description": "用户想学做菜", "priority": 4},
            {"topic": "哪家餐厅性价比高？", "category": "food", "description": "用户想找性价比餐厅", "priority": 4},
            # 兴趣类 (4-5分) - 用户想学习或了解兴趣相关
            {"topic": "怎么开始学这个？", "category": "hobby", "description": "用户想学新技能", "priority": 5},
            {"topic": "有什么入门书推荐？", "category": "hobby", "description": "用户想找学习资源", "priority": 4},
            {"topic": "这个爱好需要多少钱？", "category": "hobby", "description": "用户关心成本", "priority": 4},
            # 生活类 (4-5分) - 用户想解决生活问题
            {"topic": "怎么提高生活效率？", "category": "life", "description": "用户想优化生活", "priority": 5},
            {"topic": "如何平衡工作生活？", "category": "life", "description": "用户想平衡生活", "priority": 4},
            {"topic": "有什么健康建议？", "category": "life", "description": "用户关心健康", "priority": 4},
            # 娱乐类 (3-4分) - 用户想找娱乐内容
            {"topic": "最近有什么好电影？", "category": "entertainment", "description": "用户想找电影", "priority": 3},
            {"topic": "这首歌什么风格？", "category": "entertainment", "description": "用户想了解音乐", "priority": 3},
            {"topic": "这个游戏好玩吗？", "category": "entertainment", "description": "用户想了解游戏", "priority": 3},
            # 旅行类 (3-4分) - 用户想规划旅行
            {"topic": "这个地方值得去吗？", "category": "travel", "description": "用户想了解目的地", "priority": 4},
            {"topic": "旅行需要准备什么？", "category": "travel", "description": "用户想做旅行准备", "priority": 3},
            {"topic": "怎么规划行程？", "category": "travel", "description": "用户想规划旅行", "priority": 3},
        ]

        # 随机选择话题，增加多样性
        random.shuffle(topic_pool)
        return topic_pool
