# 头像分配服务使用指南

## 概述

头像分配服务为新建用户自动分配合适的头像。系统会根据用户的姓名、关系和属性信息智能识别角色，并从对应的头像资源池中随机选择一个头像。

## 功能特性

1. **智能角色识别**：从姓名、关系、属性中自动识别用户角色
2. **随机头像分配**：为同一角色随机分配不同头像，避免重复
3. **多角色支持**：支持妈妈、爸爸、儿子、女儿、爷爷、奶奶、朋友、同事等角色
4. **自动集成**：在创建新用户时自动调用，无需手动干预
5. **API 管理**：提供 API 接口管理头像资源池

## 支持的角色

- **妈妈**：妈妈、母亲、妈、老妈、娘
- **爸爸**：爸爸、父亲、爸、老爸、爹
- **儿子**：儿子、儿、小子、孩子
- **女儿**：女儿、女、闺女、丫头
- **爷爷**：爷爷、爷、祖父、老爷子
- **奶奶**：奶奶、奶、祖母、老奶奶
- **朋友**：朋友、好友、闺蜜、兄弟、哥们
- **同事**：同事、同僚、工友、同班
- **默认**：未识别角色时使用

## 角色识别优先级

1. **姓名识别**：优先从用户姓名中识别角色关键词
2. **关系识别**：如果姓名中没有，从关系列表中识别
3. **属性识别**：如果关系中没有，从关键属性中识别
4. **默认角色**：如果都没有识别到，使用默认角色

## API 接口

### 1. 获取头像资源池

```http
GET /humanrelation/avatar_pools
```

返回所有角色的头像资源池。

## 使用示例

### 自动分配（推荐）

创建新用户时，系统会自动调用头像分配服务：

```python
# 创建新用户时会自动分配头像
result = add_person(
    user_id="user123",
    canonical_name="妈妈",
    relationships=[],
    key_attributes={}
    # avatar 参数为空时会自动分配
)
```

### 手动分配

```python
from service.avatar_service import assign_avatar_for_new_person

avatar_url = assign_avatar_for_new_person(
    canonical_name="妈妈",
    relationships=[],
    key_attributes={}
)
```

## 更新头像资源

### 方法 1：通过 Lion 配置中心更新（推荐）

1. 在 Lion 配置中心添加配置项：

   - 配置键：`humanrelation.avatar_pools`
   - 配置值：JSON 格式的头像资源池（参考 `docs/lion_avatar_config_example.json`）

2. 配置更新后立即生效，无需重启应用或调用任何接口

### 方法 2：直接修改代码（不推荐）

编辑 `app/service/avatar_service.py` 文件中的 `_get_default_avatar_pools` 方法。

## Lion 配置中心配置

### 配置键

```
humanrelation.avatar_pools
```

### 配置值格式

参考 `docs/lion_avatar_config_example.json` 文件，配置值应为 JSON 格式：

```json
{
  "妈妈": [
    "https://your-cdn.com/avatars/mom1.jpg",
    "https://your-cdn.com/avatars/mom2.jpg"
  ],
  "爸爸": [
    "https://your-cdn.com/avatars/dad1.jpg",
    "https://your-cdn.com/avatars/dad2.jpg"
  ]
}
```

### 配置更新流程

1. 在 Lion 配置中心更新 `humanrelation.avatar_pools` 配置
2. 新的头像配置立即生效，无需重启应用或调用任何接口

## 测试

运行测试脚本验证功能：

```bash
cd /path/to/project
python app/test_avatar_service.py
```

## 注意事项

1. **头像 URL 有效性**：确保提供的头像 URL 可以正常访问
2. **角色扩展**：如需添加新角色，需要同时更新 `avatar_pools` 和 `role_keywords`
3. **随机性**：同一角色的多个头像会随机分配，确保多样性
4. **默认处理**：未识别的角色会使用默认头像池

## 日志

头像分配过程会记录详细日志，包括：

- 角色识别过程
- 头像选择结果
- 分配完成信息

查看日志可以帮助调试和监控头像分配情况。
